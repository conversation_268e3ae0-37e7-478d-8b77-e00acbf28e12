import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/localization/app_localizations.dart';
import '../../core/theme/app_colors.dart';
import '../providers/trade_provider.dart';
import '../providers/theme_provider.dart';
import '../widgets/statistics_card.dart';
import '../widgets/trade_list_item.dart';
import '../widgets/empty_state_widget.dart';
import 'add_edit_trade_screen.dart';
import 'chart_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Load current prices when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TradeProvider>().loadCurrentPrices();
    });
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.appTitle),
        actions: [
          // Refresh prices button
          Consumer<TradeProvider>(
            builder: (context, tradeProvider, child) {
              return IconButton(
                onPressed: tradeProvider.isPriceLoading
                    ? null
                    : () => tradeProvider.loadCurrentPrices(),
                icon: tradeProvider.isPriceLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : const Icon(Icons.refresh),
                tooltip: localizations.refresh,
              );
            },
          ),
          // Theme toggle button
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return IconButton(
                onPressed: () => themeProvider.toggleTheme(),
                icon: Icon(
                  themeProvider.isDarkMode
                      ? Icons.light_mode
                      : Icons.dark_mode,
                ),
                tooltip: themeProvider.isDarkMode
                    ? localizations.lightMode
                    : localizations.darkMode,
              );
            },
          ),
          // Chart button
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChartScreen(),
                ),
              );
            },
            icon: const Icon(Icons.bar_chart),
            tooltip: localizations.chart,
          ),
        ],
      ),
      body: Consumer<TradeProvider>(
        builder: (context, tradeProvider, child) {
          if (tradeProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (tradeProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    tradeProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => tradeProvider.loadTrades(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          if (tradeProvider.trades.isEmpty) {
            return EmptyStateWidget(
              title: localizations.noTradesYet,
              subtitle: localizations.addFirstTrade,
              icon: Icons.trending_up,
              onActionPressed: () => _navigateToAddTrade(context),
              actionText: localizations.addTrade,
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await tradeProvider.loadTrades();
              await tradeProvider.loadCurrentPrices();
            },
            child: CustomScrollView(
              slivers: [
                // Statistics Section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.statistics,
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: StatisticsCard(
                                title: localizations.totalProfit,
                                value: tradeProvider.totalProfit,
                                isProfit: true,
                                icon: Icons.trending_up,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: StatisticsCard(
                                title: localizations.totalLoss,
                                value: tradeProvider.totalLoss,
                                isProfit: false,
                                icon: Icons.trending_down,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        StatisticsCard(
                          title: localizations.netProfitLoss,
                          value: tradeProvider.netProfitLoss,
                          isProfit: tradeProvider.netProfitLoss >= 0,
                          icon: tradeProvider.netProfitLoss >= 0
                              ? Icons.account_balance_wallet
                              : Icons.money_off,
                          isFullWidth: true,
                        ),
                      ],
                    ),
                  ),
                ),

                // Trades Section Header
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations.trades,
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                        Text(
                          '${tradeProvider.trades.length} ${localizations.trades}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SliverToBoxAdapter(child: SizedBox(height: 8)),

                // Trades List
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final trade = tradeProvider.trades[index];
                      return TradeListItem(
                        trade: trade,
                        currentPrice: tradeProvider.currentPrices[trade.coinName],
                        onEdit: () => _navigateToEditTrade(context, trade.id),
                        onDelete: () => _showDeleteDialog(context, trade.id),
                      );
                    },
                    childCount: tradeProvider.trades.length,
                  ),
                ),

                // Bottom padding for FAB
                const SliverToBoxAdapter(child: SizedBox(height: 80)),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _navigateToAddTrade(context),
        child: const Icon(Icons.add),
        tooltip: localizations.addTrade,
      ),
    );
  }

  void _navigateToAddTrade(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditTradeScreen(),
      ),
    );
  }

  void _navigateToEditTrade(BuildContext context, String tradeId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditTradeScreen(tradeId: tradeId),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, String tradeId) {
    final localizations = AppLocalizations.of(context)!;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.deleteTrade),
        content: Text(localizations.confirmDelete),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await context.read<TradeProvider>().deleteTrade(tradeId);
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(localizations.tradeDeleted),
                    backgroundColor: AppColors.success,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }
}
      body: Consumer<TradeProvider>(
        builder = (context, tradeProvider, child) {
          if (tradeProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (tradeProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    tradeProvider.error!,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => tradeProvider.loadTrades(),
                    child: Text(localizations.refresh),
                  ),
                ],
              ),
            );
          }

          if (tradeProvider.trades.isEmpty) {
            return EmptyStateWidget(
              title: localizations.noTradesYet,
              subtitle: localizations.addFirstTrade,
              icon: Icons.trending_up,
              onActionPressed: () => _navigateToAddTrade(context),
              actionText: localizations.addTrade,
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await tradeProvider.loadTrades();
              await tradeProvider.loadCurrentPrices();
            },
            child: CustomScrollView(
              slivers: [
                // Statistics Section
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          localizations.statistics,
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: StatisticsCard(
                                title: localizations.totalProfit,
                                value: tradeProvider.totalProfit,
                                isProfit: true,
                                icon: Icons.trending_up,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: StatisticsCard(
                                title: localizations.totalLoss,
                                value: tradeProvider.totalLoss,
                                isProfit: false,
                                icon: Icons.trending_down,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        StatisticsCard(
                          title: localizations.netProfitLoss,
                          value: tradeProvider.netProfitLoss,
                          isProfit: tradeProvider.netProfitLoss >= 0,
                          icon: tradeProvider.netProfitLoss >= 0
                              ? Icons.account_balance_wallet
                              : Icons.money_off,
                          isFullWidth: true,
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Trades Section Header
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          localizations.trades,
                          style: Theme.of(context).textTheme.headlineMedium,
                        ),
                        Text(
                          '${tradeProvider.trades.length} ${localizations.trades}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SliverToBoxAdapter(child: SizedBox(height: 8)),
                
                // Trades List
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      final trade = tradeProvider.trades[index];
                      return TradeListItem(
                        trade: trade,
                        currentPrice: tradeProvider.currentPrices[trade.coinName],
                        onEdit: () => _navigateToEditTrade(context, trade.id),
                        onDelete: () => _showDeleteDialog(context, trade.id),
                      );
                    },
                    childCount: tradeProvider.trades.length,
                  ),
                ),
                
                // Bottom padding for FAB
                const SliverToBoxAdapter(child: SizedBox(height: 80)),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed = () => _navigateToAddTrade(context),
        child = const Icon(Icons.add),
        tooltip = localizations.addTrade,
      ),
    );
  }

  void _navigateToAddTrade(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddEditTradeScreen(),
      ),
    );
  }

  void _navigateToEditTrade(BuildContext context, String tradeId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddEditTradeScreen(tradeId: tradeId),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, String tradeId) {
    final localizations = AppLocalizations.of(context)!;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(localizations.deleteTrade),
        content: Text(localizations.confirmDelete),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(localizations.cancel),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await context.read<TradeProvider>().deleteTrade(tradeId);
              if (success && mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(localizations.tradeDeleted),
                    backgroundColor: AppColors.success,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: Text(localizations.delete),
          ),
        ],
      ),
    );
  }
}
