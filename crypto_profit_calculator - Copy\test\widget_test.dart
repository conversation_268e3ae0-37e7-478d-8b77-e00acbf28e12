import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:crypto_profit_calculator/main.dart';

void main() {
  group('Crypto Profit Calculator Tests', () {
    testWidgets('App should start without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyApp());

      // Verify that the app starts without throwing any exceptions
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('Home screen should display app title', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify that the app title is displayed
      expect(find.text('حاسبة أرباح العملات الرقمية'), findsOneWidget);
    });

    testWidgets('FAB should be present for adding trades', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify that the floating action button is present
      expect(find.byType(FloatingActionButton), findsOneWidget);
    });

    testWidgets('Empty state should be shown when no trades exist', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Verify that empty state is shown
      expect(find.text('لا توجد صفقات بعد'), findsOneWidget);
      expect(find.text('أضف أول صفقة لك'), findsOneWidget);
    });

    testWidgets('Navigation to add trade screen should work', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Tap the FAB to navigate to add trade screen
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pumpAndSettle();

      // Verify that we navigated to the add trade screen
      expect(find.text('إضافة صفقة'), findsOneWidget);
    });

    testWidgets('Theme toggle should work', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Find and tap the theme toggle button
      final themeButton = find.byIcon(Icons.dark_mode);
      if (themeButton.evaluate().isNotEmpty) {
        await tester.tap(themeButton);
        await tester.pumpAndSettle();
        
        // Verify that the theme changed (icon should change)
        expect(find.byIcon(Icons.light_mode), findsOneWidget);
      }
    });

    testWidgets('Chart screen navigation should work', (WidgetTester tester) async {
      await tester.pumpWidget(const MyApp());
      await tester.pumpAndSettle();

      // Find and tap the chart button
      final chartButton = find.byIcon(Icons.bar_chart);
      await tester.tap(chartButton);
      await tester.pumpAndSettle();

      // Verify that we navigated to the chart screen
      expect(find.text('الرسم البياني'), findsOneWidget);
    });
  });

  group('Trade Model Tests', () {
    test('Trade profit calculation should be correct', () {
      // This would require importing the Trade model
      // For now, we'll test the calculation logic manually
      
      const buyPrice = 50000.0;
      const sellPrice = 55000.0;
      const quantity = 0.1;
      
      final totalBuyValue = buyPrice * quantity;
      final totalSellValue = sellPrice * quantity;
      final profitLoss = totalSellValue - totalBuyValue;
      
      expect(totalBuyValue, equals(5000.0));
      expect(totalSellValue, equals(5500.0));
      expect(profitLoss, equals(500.0));
    });

    test('Trade loss calculation should be correct', () {
      const buyPrice = 55000.0;
      const sellPrice = 50000.0;
      const quantity = 0.1;
      
      final totalBuyValue = buyPrice * quantity;
      final totalSellValue = sellPrice * quantity;
      final profitLoss = totalSellValue - totalBuyValue;
      
      expect(totalBuyValue, equals(5500.0));
      expect(totalSellValue, equals(5000.0));
      expect(profitLoss, equals(-500.0));
    });

    test('Profit percentage calculation should be correct', () {
      const buyPrice = 50000.0;
      const sellPrice = 55000.0;
      
      final profitPercentage = ((sellPrice - buyPrice) / buyPrice) * 100;
      
      expect(profitPercentage, equals(10.0));
    });
  });

  group('Formatter Tests', () {
    test('Currency formatting should work correctly', () {
      // Test Arabic number formatting
      const amount = 1234.56;
      final formatted = '\$${amount.toStringAsFixed(2)}';
      
      expect(formatted, equals('\$1234.56'));
    });

    test('Percentage formatting should work correctly', () {
      const percentage = 15.75;
      final formatted = '${percentage.toStringAsFixed(2)}%';
      
      expect(formatted, equals('15.75%'));
    });
  });
}
