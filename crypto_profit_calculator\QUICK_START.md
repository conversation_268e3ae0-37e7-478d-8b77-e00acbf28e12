# 🚀 دليل التشغيل السريع

## ✅ المتطلبات
- **Flutter SDK 3.10+** مثبت على النظام
- **Android Studio** أو **VS Code** مع إضافات Flutter
- **جهاز Android** متصل أو **محاكي Android** يعمل

## 🏃‍♂️ التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل التلقائي
```bash
# في Windows
double-click على run_app.bat

# أو في PowerShell
.\run_app.ps1
```

### الطريقة الثانية: الأوامر اليدوية
```bash
# 1. تثبيت التبعيات
flutter pub get

# 2. توليد الملفات المطلوبة
flutter packages pub run build_runner build

# 3. تشغيل التطبيق
flutter run
```

## 📱 التحقق من الجهاز
```bash
# عرض الأجهزة المتاحة
flutter devices

# تشغيل على جهاز محدد
flutter run -d [device-id]
```

## 🔧 حل المشاكل السريع

### مشكلة: Flutter غير معروف
```bash
# تحقق من تثبيت Flutter
flutter doctor

# إضافة Flutter إلى PATH
# Windows: إضافة مجلد Flutter\bin إلى متغير البيئة PATH
```

### مشكلة: خطأ في build_runner
```bash
flutter clean
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### مشكلة: لا يوجد جهاز متصل
- تأكد من تفعيل **USB Debugging** في جهاز Android
- أو قم بتشغيل **Android Emulator** من Android Studio

## 🎯 الميزات الرئيسية للتطبيق

### ➕ إضافة صفقة جديدة
1. اضغط على زر **+** في الشاشة الرئيسية
2. أدخل تفاصيل الصفقة:
   - **التاريخ**: اختر من التقويم
   - **اسم العملة**: اختر من القائمة أو اكتب (مثل BTC, ETH)
   - **سعر الشراء**: السعر الذي اشتريت به
   - **سعر البيع**: السعر الذي بعت به
   - **الكمية**: كمية العملة
3. اضغط **حفظ**

### 📊 عرض الإحصائيات
- **إجمالي الأرباح**: مجموع جميع الأرباح
- **إجمالي الخسائر**: مجموع جميع الخسائر  
- **صافي الربح/الخسارة**: الناتج النهائي

### 📈 الرسوم البيانية
اضغط على أيقونة **الرسم البياني** لعرض:
- **رسم الأرباح/الخسائر**: لكل صفقة
- **الرسم التراكمي**: تطور الأرباح عبر الزمن
- **توزيع العملات**: نسبة كل عملة من الأرباح

### 🌐 الأسعار اللحظية
- اضغط **تحديث** لجلب الأسعار الحالية
- في شاشة الإضافة، اضغط أيقونة **السعر** لملء السعر الحالي تلقائياً

### 🌙 الوضع الليلي
اضغط على أيقونة **القمر/الشمس** في الشاشة الرئيسية لتبديل الوضع

## 📞 المساعدة
- راجع **SETUP_GUIDE.md** للتفاصيل الكاملة
- راجع **README.md** لمعلومات المشروع
- في حالة وجود مشاكل، تحقق من **flutter doctor**

---
**نصيحة**: احفظ نسخة احتياطية من بياناتك بانتظام! 💾
