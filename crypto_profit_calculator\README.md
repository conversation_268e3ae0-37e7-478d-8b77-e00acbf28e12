# حاسبة أرباح العملات الرقمية 📈

تطبيق Flutter متكامل لحساب الربح والخسارة في تداول العملات الرقمية مع واجهة عربية احترافية تدعم RTL.

## 🌟 المميزات الرئيسية

### 📊 إدارة الصفقات
- إضافة وتعديل وحذف صفقات التداول
- حفظ تفاصيل كاملة لكل صفقة (التاريخ، العملة، أسعار الشراء والبيع، الكمية)
- عرض قائمة منظمة بجميع الصفقات مع إمكانية البحث والفلترة

### 🧮 العمليات الحسابية التلقائية
- حساب القيمة الإجمالية للشراء والبيع
- حساب الربح أو الخسارة لكل صفقة
- حساب نسبة الربح/الخسارة المئوية
- إحصائيات شاملة (إجمالي الأرباح، الخسائر، صافي الربح)

### 📈 الرسوم البيانية التفاعلية
- رسم بياني خطي لتطور الأرباح والخسائر
- رسم بياني تراكمي للأرباح
- رسم بياني دائري لتوزيع الأرباح حسب العملة
- رسوم بيانية تفاعلية مع إمكانية التكبير والتصغير

### 🌐 ربط API للأسعار اللحظية
- جلب الأسعار الحالية للعملات الرقمية من CoinGecko API
- تحديث تلقائي للأسعار
- دعم أكثر من 30 عملة رقمية شائعة

### 🎨 واجهة مستخدم احترافية
- تصميم Material Design 3
- دعم كامل للغة العربية مع RTL
- الوضع الليلي والنهاري
- تصميم متجاوب يتكيف مع أحجام الشاشات المختلفة
- ألوان مميزة للأرباح (أخضر) والخسائر (أحمر)

### 💾 تخزين محلي آمن
- استخدام Hive لتخزين البيانات محلياً
- عدم الحاجة للاتصال بالإنترنت للوظائف الأساسية
- نسخ احتياطي تلقائي للبيانات

## 🏗️ الهيكل البرمجي

### Clean Architecture
```
lib/
├── core/                    # الأساسيات المشتركة
│   ├── constants/          # الثوابت
│   ├── localization/       # الترجمة والتعريب
│   ├── theme/             # الألوان والثيمات
│   └── utils/             # الأدوات المساعدة
├── data/                   # طبقة البيانات
│   ├── datasources/       # مصادر البيانات
│   ├── models/            # نماذج البيانات
│   └── repositories/      # مستودعات البيانات
└── presentation/          # طبقة العرض
    ├── providers/         # إدارة الحالة
    ├── screens/          # الشاشات
    └── widgets/          # المكونات المعاد استخدامها
```

## 📱 الشاشات الرئيسية

### 🏠 الشاشة الرئيسية
- عرض الإحصائيات الأساسية
- قائمة بجميع الصفقات
- أزرار سريعة للإضافة والتحديث

### ➕ شاشة إضافة/تعديل الصفقة
- نموذج شامل لإدخال بيانات الصفقة
- اختيار التاريخ باستخدام Date Picker
- قائمة منسدلة بالعملات المدعومة
- معاينة فورية للحسابات

### 📊 شاشة الرسوم البيانية
- ثلاثة أنواع من الرسوم البيانية
- تبديل سهل بين الأنواع المختلفة
- عرض تفاعلي مع التفاصيل

## 🛠️ التقنيات المستخدمة

### Frontend
- **Flutter 3.10+** - إطار العمل الأساسي
- **Dart 3.0+** - لغة البرمجة
- **Material Design 3** - نظام التصميم

### إدارة الحالة
- **Provider** - إدارة حالة التطبيق
- **ChangeNotifier** - للتحديثات التفاعلية

### قاعدة البيانات المحلية
- **Hive** - قاعدة بيانات NoSQL سريعة
- **Hive Generator** - لتوليد TypeAdapters

### الرسوم البيانية
- **FL Chart** - مكتبة الرسوم البيانية التفاعلية

### الشبكة والAPI
- **HTTP** - للاتصال بـ APIs
- **CoinGecko API** - للحصول على أسعار العملات

### التعريب والتنسيق
- **Flutter Localizations** - دعم اللغات المتعددة
- **Intl** - تنسيق الأرقام والتواريخ

## 🚀 كيفية التشغيل

### المتطلبات
- Flutter SDK 3.10 أو أحدث
- Dart SDK 3.0 أو أحدث
- Android Studio أو VS Code
- جهاز Android/iOS أو محاكي

### خطوات التشغيل
1. **استنساخ المشروع**
   ```bash
   git clone [repository-url]
   cd crypto_profit_calculator
   ```

2. **تثبيت التبعيات**
   ```bash
   flutter pub get
   ```

3. **توليد الملفات المطلوبة**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **تشغيل التطبيق**
   ```bash
   flutter run
   ```

## 📦 التبعيات الرئيسية

```yaml
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  provider: ^6.1.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  fl_chart: ^0.65.0
  intl: ^0.18.1
  http: ^1.1.0

dev_dependencies:
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
```

## 🎯 الميزات المستقبلية

- [ ] إضافة المزيد من العملات الرقمية
- [ ] تصدير البيانات إلى Excel/PDF
- [ ] إشعارات تغيير الأسعار
- [ ] ربط مع منصات التداول
- [ ] تحليلات متقدمة للأداء
- [ ] مشاركة الإحصائيات على وسائل التواصل

## 🤝 المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى Branch (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- **المطور**: [اسمك]
- **البريد الإلكتروني**: [بريدك الإلكتروني]
- **GitHub**: [رابط GitHub]

---

**ملاحظة**: هذا التطبيق مخصص للأغراض التعليمية والشخصية. يرجى استشارة خبير مالي قبل اتخاذ قرارات استثمارية.
