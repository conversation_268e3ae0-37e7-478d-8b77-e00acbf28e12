# دليل إعداد وتشغيل تطبيق حاسبة أرباح العملات الرقمية

## 📋 المتطلبات الأساسية

### 1. تثبيت Flutter SDK
```bash
# تحميل Flutter SDK من الموقع الرسمي
https://flutter.dev/docs/get-started/install

# إضافة Flutter إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"

# التحقق من التثبيت
flutter doctor
```

### 2. إعداد بيئة التطوير
- **Android Studio** أو **VS Code** مع إضافات Flutter و Dart
- **Android SDK** للتطوير على Android
- **Xcode** للتطوير على iOS (macOS فقط)

## 🚀 خطوات التشغيل

### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd crypto_profit_calculator
```

### 2. تثبيت التبعيات
```bash
flutter pub get
```

### 3. توليد الملفات المطلوبة
```bash
# توليد ملفات Hive TypeAdapter
flutter packages pub run build_runner build

# في حالة وجود تعارض، استخدم:
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 4. إعداد الأذونات (Android)
تأكد من وجود الأذونات التالية في `android/app/src/main/AndroidManifest.xml`:
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 5. تشغيل التطبيق
```bash
# تشغيل في وضع التطوير
flutter run

# تشغيل في وضع الإصدار
flutter run --release

# تشغيل على جهاز محدد
flutter run -d [device-id]
```

## 🔧 إعدادات إضافية

### إعداد VS Code
إضافة الإضافات التالية:
- Flutter
- Dart
- Flutter Intl (للترجمة)
- Bracket Pair Colorizer (اختياري)

### إعداد Android Studio
- تثبيت Flutter و Dart plugins
- إعداد Android SDK
- إنشاء AVD للاختبار

## 🧪 تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
flutter test

# تشغيل اختبارات محددة
flutter test test/widget_test.dart

# تشغيل الاختبارات مع تقرير التغطية
flutter test --coverage
```

## 📱 بناء التطبيق للإصدار

### Android APK
```bash
# بناء APK
flutter build apk

# بناء APK مقسم حسب المعمارية
flutter build apk --split-per-abi

# بناء App Bundle (مفضل لـ Google Play)
flutter build appbundle
```

### iOS
```bash
# بناء للـ iOS (macOS فقط)
flutter build ios

# بناء IPA
flutter build ipa
```

## 🐛 حل المشاكل الشائعة

### مشكلة: Flutter doctor يظهر أخطاء
```bash
# تحديث Flutter
flutter upgrade

# إعادة تثبيت التبعيات
flutter clean
flutter pub get
```

### مشكلة: خطأ في build_runner
```bash
# حذف الملفات المولدة وإعادة التوليد
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### مشكلة: خطأ في Hive
```bash
# التأكد من تسجيل TypeAdapters في main.dart
Hive.registerAdapter(TradeAdapter());
```

### مشكلة: خطأ في الترجمة
```bash
# توليد ملفات الترجمة
flutter gen-l10n
```

## 📊 مراقبة الأداء

### تحليل حجم التطبيق
```bash
flutter build apk --analyze-size
```

### تحليل الأداء
```bash
flutter run --profile
```

## 🔄 التحديثات والصيانة

### تحديث التبعيات
```bash
# عرض التبعيات القديمة
flutter pub outdated

# تحديث التبعيات
flutter pub upgrade
```

### تنظيف المشروع
```bash
flutter clean
flutter pub get
```

## 📝 ملاحظات مهمة

1. **API Key**: التطبيق يستخدم CoinGecko API المجاني، لا حاجة لـ API key
2. **الأذونات**: تأكد من إضافة أذونات الإنترنت للـ Android
3. **RTL Support**: التطبيق يدعم RTL بشكل كامل
4. **الخطوط**: تأكد من إضافة خط Cairo للعربية
5. **الاختبار**: اختبر على أجهزة مختلفة للتأكد من التوافق

## 🆘 الحصول على المساعدة

- **Flutter Documentation**: https://flutter.dev/docs
- **Dart Documentation**: https://dart.dev/guides
- **Stack Overflow**: استخدم tags `flutter` و `dart`
- **GitHub Issues**: لمشاكل محددة بالمشروع

## 📞 التواصل

في حالة وجود مشاكل أو استفسارات:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير
- مراجعة الوثائق الرسمية

---

**نصيحة**: احتفظ بنسخة احتياطية من بياناتك قبل التحديثات الكبيرة!
