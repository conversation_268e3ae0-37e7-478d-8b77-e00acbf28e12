# 🔧 دليل حل المشاكل

## 🚨 المشكلة الأساسية: Flutter غير مثبت

### الأعراض:
```
flutter : The term 'flutter' is not recognized
```

### الحل الكامل:

#### 1️⃣ **تحميل Flutter SDK**
- اذهب إلى: https://flutter.dev/docs/get-started/install/windows
- حمل **Flutter SDK** للـ Windows (حوالي 1.5 GB)
- استخرج الملف المضغوط إلى `C:\flutter`

#### 2️⃣ **إضافة Flutter إلى PATH**
```powershell
# الطريقة الأولى: عبر واجهة Windows
1. اضغط Win + R واكتب: sysdm.cpl
2. اذهب إلى Advanced → Environment Variables
3. في System Variables، اختر Path واضغط Edit
4. اضغط New وأضف: C:\flutter\bin
5. اضغط OK واخرج

# الطريقة الثانية: عبر PowerShell (مؤقت)
$env:PATH += ";C:\flutter\bin"
```

#### 3️⃣ **التحقق من التثبيت**
```powershell
# أعد تشغيل PowerShell ثم:
flutter doctor
```

## 🔍 **أداة التشخيص السريع**
```powershell
# شغل أداة التشخيص
.\diagnose.ps1
```

## 📱 **مشاكل الأجهزة**

### المشكلة: "No devices detected"

#### الحلول:
1. **جهاز Android:**
   ```
   - فعل Developer Options
   - فعل USB Debugging
   - وصل الجهاز بكابل USB
   ```

2. **Android Emulator:**
   ```
   - ثبت Android Studio
   - أنشئ AVD (Android Virtual Device)
   - شغل المحاكي
   ```

3. **Chrome (للتطوير السريع):**
   ```powershell
   flutter run -d chrome
   ```

## 🏗️ **مشاكل البناء**

### المشكلة: خطأ في build_runner
```powershell
# الحل
flutter clean
flutter pub get
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### المشكلة: خطأ في التبعيات
```powershell
# الحل
flutter clean
flutter pub cache repair
flutter pub get
```

## 🌐 **مشاكل الشبكة**

### المشكلة: فشل تحميل التبعيات
```powershell
# تغيير مرآة pub.dev
flutter pub cache repair
flutter pub get --verbose
```

## 🔧 **أوامر مفيدة للتشخيص**

```powershell
# فحص شامل للنظام
flutter doctor -v

# عرض الأجهزة المتاحة
flutter devices

# فحص التبعيات
flutter pub deps

# تنظيف المشروع
flutter clean

# إعادة تثبيت التبعيات
flutter pub get

# تشغيل مع تفاصيل أكثر
flutter run --verbose
```

## 📋 **قائمة التحقق السريعة**

- [ ] ✅ Flutter مثبت ومضاف إلى PATH
- [ ] ✅ Android Studio مثبت (اختياري لكن مفيد)
- [ ] ✅ جهاز Android متصل أو محاكي يعمل
- [ ] ✅ USB Debugging مفعل
- [ ] ✅ ملفات المشروع موجودة
- [ ] ✅ التبعيات مثبتة (`flutter pub get`)
- [ ] ✅ ملفات Hive مولدة (`build_runner`)

## 🆘 **إذا لم تنجح الحلول**

### 1. **إعادة تثبيت Flutter**
```powershell
# احذف مجلد C:\flutter
# حمل Flutter مرة أخرى
# أعد إضافته إلى PATH
```

### 2. **استخدام Flutter من مجلد محلي**
```powershell
# إذا لم تستطع إضافة PATH
C:\flutter\bin\flutter.exe doctor
C:\flutter\bin\flutter.exe run
```

### 3. **تشغيل على الويب**
```powershell
# لا يحتاج جهاز Android
flutter config --enable-web
flutter run -d chrome
```

## 📞 **طلب المساعدة**

إذا استمرت المشاكل:
1. شغل `.\diagnose.ps1` وانسخ النتائج
2. شغل `flutter doctor -v` وانسخ النتائج
3. اذكر نظام التشغيل والإصدار
4. اذكر الخطوات التي جربتها

## 🎯 **نصائح للنجاح**

1. **تأكد من الاتصال بالإنترنت** عند أول تشغيل
2. **استخدم PowerShell كمدير** إذا واجهت مشاكل أذونات
3. **أعد تشغيل الكمبيوتر** بعد تثبيت Flutter
4. **استخدم مجلد بدون مسافات** لـ Flutter (مثل C:\flutter)
5. **تأكد من وجود مساحة كافية** (5+ GB)

---

**💡 نصيحة**: ابدأ بـ `.\diagnose.ps1` لتحديد المشكلة بدقة!
