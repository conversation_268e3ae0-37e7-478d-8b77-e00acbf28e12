# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at https://dart.dev/lints.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
    
    # Additional rules for better code quality
    prefer_const_constructors: true
    prefer_const_literals_to_create_immutables: true
    prefer_const_declarations: true
    prefer_final_fields: true
    prefer_final_locals: true
    prefer_final_in_for_each: true
    avoid_unnecessary_containers: true
    avoid_web_libraries_in_flutter: true
    sized_box_for_whitespace: true
    use_key_in_widget_constructors: true
    prefer_const_constructors_in_immutables: true
    avoid_function_literals_in_foreach_calls: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    prefer_if_null_operators: true
    prefer_null_aware_operators: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_nullable_for_final_variable_declarations: true
    use_build_context_synchronously: true
    
# Additional options
analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "**/generated_plugin_registrant.dart"
  
  strong-mode:
    implicit-casts: false
    implicit-dynamic: false
  
  errors:
    # Treat missing required parameters as errors
    missing_required_param: error
    # Treat missing returns as errors
    missing_return: error
    # Treat invalid assignments as errors
    invalid_assignment: error
