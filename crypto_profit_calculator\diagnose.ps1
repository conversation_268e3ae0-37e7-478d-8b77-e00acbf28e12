# أداة تشخيص مشاكل Flutter
Write-Host "🔍 أداة تشخيص مشاكل Flutter" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

# 1. التحقق من Flutter
Write-Host "1️⃣ التحقق من Flutter..." -ForegroundColor Yellow
$flutterPath = Get-Command flutter -ErrorAction SilentlyContinue
if ($flutterPath) {
    Write-Host "✅ Flutter موجود في: $($flutterPath.Source)" -ForegroundColor Green
    try {
        flutter --version
    } catch {
        Write-Host "❌ خطأ في تشغيل Flutter" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Flutter غير موجود في PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 خطوات الحل:" -ForegroundColor Yellow
    Write-Host "1. حمل Flutter من: https://flutter.dev/docs/get-started/install/windows"
    Write-Host "2. استخرج إلى C:\flutter"
    Write-Host "3. أضف C:\flutter\bin إلى PATH"
    Write-Host ""
}

Write-Host ""

# 2. التحقق من PATH
Write-Host "2️⃣ التحقق من PATH..." -ForegroundColor Yellow
$pathEntries = $env:PATH -split ";"
$flutterInPath = $pathEntries | Where-Object { $_ -like "*flutter*" }
if ($flutterInPath) {
    Write-Host "✅ Flutter موجود في PATH:" -ForegroundColor Green
    $flutterInPath | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ Flutter غير موجود في PATH" -ForegroundColor Red
    Write-Host "💡 لإضافة Flutter مؤقتاً:" -ForegroundColor Cyan
    Write-Host '$env:PATH += ";C:\flutter\bin"' -ForegroundColor Gray
}

Write-Host ""

# 3. التحقق من الأجهزة
Write-Host "3️⃣ التحقق من الأجهزة..." -ForegroundColor Yellow
if ($flutterPath) {
    try {
        $devices = flutter devices 2>&1
        if ($devices -match "No devices detected") {
            Write-Host "❌ لا توجد أجهزة متصلة" -ForegroundColor Red
            Write-Host ""
            Write-Host "🔧 الحلول:" -ForegroundColor Yellow
            Write-Host "• وصل جهاز Android مع USB Debugging"
            Write-Host "• شغل Android Emulator"
            Write-Host "• استخدم Chrome: flutter run -d chrome"
        } else {
            Write-Host "✅ الأجهزة المتاحة:" -ForegroundColor Green
            Write-Host $devices
        }
    } catch {
        Write-Host "❌ خطأ في فحص الأجهزة" -ForegroundColor Red
    }
} else {
    Write-Host "⏭️ تخطي فحص الأجهزة (Flutter غير متاح)" -ForegroundColor Yellow
}

Write-Host ""

# 4. التحقق من Android Studio
Write-Host "4️⃣ التحقق من Android Studio..." -ForegroundColor Yellow
$androidStudioPaths = @(
    "${env:ProgramFiles}\Android\Android Studio",
    "${env:ProgramFiles(x86)}\Android\Android Studio",
    "${env:LOCALAPPDATA}\Android\Sdk"
)

$androidStudioFound = $false
foreach ($path in $androidStudioPaths) {
    if (Test-Path $path) {
        Write-Host "✅ Android Studio موجود في: $path" -ForegroundColor Green
        $androidStudioFound = $true
        break
    }
}

if (-not $androidStudioFound) {
    Write-Host "❌ Android Studio غير موجود" -ForegroundColor Red
    Write-Host "💡 حمل من: https://developer.android.com/studio" -ForegroundColor Cyan
}

Write-Host ""

# 5. التحقق من ملفات المشروع
Write-Host "5️⃣ التحقق من ملفات المشروع..." -ForegroundColor Yellow
$requiredFiles = @(
    "pubspec.yaml",
    "lib\main.dart",
    "lib\data\models\trade.dart",
    "lib\presentation\screens\home_screen.dart"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file مفقود" -ForegroundColor Red
    }
}

Write-Host ""

# 6. فحص التبعيات
Write-Host "6️⃣ فحص التبعيات..." -ForegroundColor Yellow
if (Test-Path "pubspec.yaml") {
    $pubspec = Get-Content "pubspec.yaml" -Raw
    $dependencies = @("flutter", "provider", "hive", "fl_chart", "http", "intl")
    
    foreach ($dep in $dependencies) {
        if ($pubspec -match $dep) {
            Write-Host "✅ $dep" -ForegroundColor Green
        } else {
            Write-Host "❌ $dep مفقود" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ pubspec.yaml مفقود" -ForegroundColor Red
}

Write-Host ""

# 7. الخلاصة والتوصيات
Write-Host "📋 الخلاصة والتوصيات:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

if (-not $flutterPath) {
    Write-Host "🚨 أولوية عالية: تثبيت Flutter" -ForegroundColor Red
    Write-Host "   1. حمل Flutter SDK"
    Write-Host "   2. أضف إلى PATH"
    Write-Host "   3. أعد تشغيل Terminal"
}

if ($flutterPath -and ($devices -match "No devices detected")) {
    Write-Host "⚠️  أولوية متوسطة: إعداد جهاز للتطوير" -ForegroundColor Yellow
    Write-Host "   1. وصل جهاز Android"
    Write-Host "   2. أو شغل Android Emulator"
    Write-Host "   3. أو استخدم Chrome للتطوير"
}

if (-not $androidStudioFound) {
    Write-Host "💡 اختياري: تثبيت Android Studio" -ForegroundColor Cyan
    Write-Host "   يسهل إدارة المحاكيات والـ SDK"
}

Write-Host ""
Write-Host "🎯 الخطوة التالية:" -ForegroundColor Green
if ($flutterPath) {
    Write-Host "شغل: .\run_app.ps1" -ForegroundColor White
} else {
    Write-Host "ثبت Flutter أولاً، ثم شغل: .\run_app.ps1" -ForegroundColor White
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
