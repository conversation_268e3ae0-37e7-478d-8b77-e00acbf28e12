class AppConstants {
  // App Information
  static const String appName = 'حاسبة أرباح العملات الرقمية';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String coinGeckoBaseUrl = 'https://api.coingecko.com/api/v3';
  static const String binanceBaseUrl = 'https://api.binance.com/api/v3';
  
  // Local Storage Keys
  static const String tradesBoxName = 'trades';
  static const String settingsBoxName = 'settings';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Supported Cryptocurrencies
  static const List<String> supportedCoins = [
    'BTC', 'ETH', 'BNB', 'ADA', 'SOL', 'XRP', 'DOT', 'DOGE',
    'AVAX', 'SHIB', 'MATIC', 'LTC', 'UNI', 'LINK', 'ATOM',
    'FTM', 'ALGO', 'VET', 'ICP', 'THETA', 'FIL', 'TRX',
    'ETC', 'XLM', 'AAVE', 'CAKE', 'GRT', 'ENJ', 'CHZ'
  ];
  
  // Popular Trading Pairs
  static const Map<String, String> popularPairs = {
    'BTC/USDT': 'bitcoin',
    'ETH/USDT': 'ethereum',
    'BNB/USDT': 'binancecoin',
    'ADA/USDT': 'cardano',
    'SOL/USDT': 'solana',
    'XRP/USDT': 'ripple',
    'DOT/USDT': 'polkadot',
    'DOGE/USDT': 'dogecoin',
  };
  
  // Chart Configuration
  static const int maxChartDataPoints = 100;
  static const double chartAnimationDuration = 1.5;
  
  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Network Configuration
  static const Duration networkTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  
  // Validation Rules
  static const double minTradeAmount = 0.01;
  static const double maxTradeAmount = 1000000.0;
  static const double minQuantity = 0.00000001;
  static const double maxQuantity = 1000000000.0;
  
  // Date Ranges
  static final DateTime minTradeDate = DateTime(2009, 1, 3); // Bitcoin genesis block
  static final DateTime maxTradeDate = DateTime.now();
  
  // Error Messages
  static const String networkErrorMessage = 'خطأ في الاتصال بالشبكة';
  static const String dataNotFoundMessage = 'لم يتم العثور على البيانات';
  static const String invalidInputMessage = 'بيانات غير صحيحة';
  static const String serverErrorMessage = 'خطأ في الخادم';
  
  // Success Messages
  static const String tradeAddedMessage = 'تم إضافة الصفقة بنجاح';
  static const String tradeUpdatedMessage = 'تم تحديث الصفقة بنجاح';
  static const String tradeDeletedMessage = 'تم حذف الصفقة بنجاح';
  
  // File Paths
  static const String assetsPath = 'assets';
  static const String imagesPath = '$assetsPath/images';
  static const String iconsPath = '$assetsPath/icons';
  static const String fontsPath = '$assetsPath/fonts';
  
  // Regex Patterns
  static const String emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phonePattern = r'^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\./0-9]*$';
  static const String numberPattern = r'^\d*\.?\d*$';
  
  // Feature Flags
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;
  
  // Cache Configuration
  static const Duration cacheExpiration = Duration(minutes: 5);
  static const int maxCacheSize = 100;
}
