import 'package:intl/intl.dart';

class AppFormatters {
  // Currency formatter for Arabic locale
  static final currencyFormatter = NumberFormat.currency(
    locale: 'ar_SA',
    symbol: '\$',
    decimalDigits: 2,
  );

  // Number formatter for Arabic locale
  static final numberFormatter = NumberFormat.decimalPattern('ar_SA');

  // Percentage formatter
  static final percentageFormatter = NumberFormat.percentPattern('ar_SA');

  // Date formatter for Arabic locale
  static final dateFormatter = DateFormat('dd/MM/yyyy', 'ar_SA');
  static final dateTimeFormatter = DateFormat('dd/MM/yyyy HH:mm', 'ar_SA');

  // Compact number formatter
  static final compactFormatter = NumberFormat.compact(locale: 'ar_SA');

  // Format currency with proper Arabic formatting
  static String formatCurrency(double amount) {
    return currencyFormatter.format(amount);
  }

  // Format number with proper Arabic formatting
  static String formatNumber(double number) {
    return numberFormatter.format(number);
  }

  // Format percentage
  static String formatPercentage(double percentage) {
    return '${percentage.toStringAsFixed(2)}%';
  }

  // Format date
  static String formatDate(DateTime date) {
    return dateFormatter.format(date);
  }

  // Format date and time
  static String formatDateTime(DateTime dateTime) {
    return dateTimeFormatter.format(dateTime);
  }

  // Format compact number (e.g., 1.2K, 1.5M)
  static String formatCompact(double number) {
    return compactFormatter.format(number);
  }

  // Format profit/loss with sign and color indication
  static String formatProfitLoss(double amount) {
    final sign = amount >= 0 ? '+' : '';
    return '$sign${formatCurrency(amount)}';
  }

  // Format large numbers with K, M, B suffixes
  static String formatLargeNumber(double number) {
    if (number.abs() >= 1000000000) {
      return '${(number / 1000000000).toStringAsFixed(1)}B';
    } else if (number.abs() >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number.abs() >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return number.toStringAsFixed(2);
    }
  }

  // Parse Arabic numbers to double
  static double? parseArabicNumber(String text) {
    // Replace Arabic-Indic digits with Western digits
    final westernText = text
        .replaceAll('٠', '0')
        .replaceAll('١', '1')
        .replaceAll('٢', '2')
        .replaceAll('٣', '3')
        .replaceAll('٤', '4')
        .replaceAll('٥', '5')
        .replaceAll('٦', '6')
        .replaceAll('٧', '7')
        .replaceAll('٨', '8')
        .replaceAll('٩', '9')
        .replaceAll('٫', '.')
        .replaceAll('٬', ',');
    
    return double.tryParse(westernText);
  }

  // Validate currency input
  static bool isValidCurrency(String text) {
    final parsed = parseArabicNumber(text);
    return parsed != null && parsed > 0;
  }

  // Validate quantity input
  static bool isValidQuantity(String text) {
    final parsed = parseArabicNumber(text);
    return parsed != null && parsed > 0;
  }
}
