import 'package:hive/hive.dart';
import '../models/trade.dart';

abstract class LocalDataSource {
  Future<List<Trade>> getAllTrades();
  Future<void> addTrade(Trade trade);
  Future<void> updateTrade(Trade trade);
  Future<void> deleteTrade(String id);
  Future<Trade?> getTradeById(String id);
  Future<void> deleteAllTrades();
}

class LocalDataSourceImpl implements LocalDataSource {
  static const String _tradesBoxName = 'trades';
  
  Box<Trade> get _tradesBox => Hive.box<Trade>(_tradesBoxName);

  @override
  Future<List<Trade>> getAllTrades() async {
    try {
      final trades = _tradesBox.values.toList();
      // Sort by date (newest first)
      trades.sort((a, b) => b.date.compareTo(a.date));
      return trades;
    } catch (e) {
      throw Exception('Failed to get trades: $e');
    }
  }

  @override
  Future<void> addTrade(Trade trade) async {
    try {
      await _tradesBox.put(trade.id, trade);
    } catch (e) {
      throw Exception('Failed to add trade: $e');
    }
  }

  @override
  Future<void> updateTrade(Trade trade) async {
    try {
      await _tradesBox.put(trade.id, trade);
    } catch (e) {
      throw Exception('Failed to update trade: $e');
    }
  }

  @override
  Future<void> deleteTrade(String id) async {
    try {
      await _tradesBox.delete(id);
    } catch (e) {
      throw Exception('Failed to delete trade: $e');
    }
  }

  @override
  Future<Trade?> getTradeById(String id) async {
    try {
      return _tradesBox.get(id);
    } catch (e) {
      throw Exception('Failed to get trade by id: $e');
    }
  }

  @override
  Future<void> deleteAllTrades() async {
    try {
      await _tradesBox.clear();
    } catch (e) {
      throw Exception('Failed to delete all trades: $e');
    }
  }
}
