import 'dart:convert';
import 'package:http/http.dart' as http;

abstract class RemoteDataSource {
  Future<double> getCurrentPrice(String coinId);
  Future<Map<String, double>> getMultiplePrices(List<String> coinIds);
}

class RemoteDataSourceImpl implements RemoteDataSource {
  static const String _baseUrl = 'https://api.coingecko.com/api/v3';
  
  final http.Client client;
  
  RemoteDataSourceImpl({required this.client});

  @override
  Future<double> getCurrentPrice(String coinId) async {
    try {
      final response = await client.get(
        Uri.parse('$_baseUrl/simple/price?ids=$coinId&vs_currencies=usd'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final price = data[coinId]?['usd'];
        if (price != null) {
          return price.toDouble();
        } else {
          throw Exception('Price not found for $coinId');
        }
      } else {
        throw Exception('Failed to fetch price: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }

  @override
  Future<Map<String, double>> getMultiplePrices(List<String> coinIds) async {
    try {
      final coinIdsString = coinIds.join(',');
      final response = await client.get(
        Uri.parse('$_baseUrl/simple/price?ids=$coinIdsString&vs_currencies=usd'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as Map<String, dynamic>;
        final prices = <String, double>{};
        
        for (final entry in data.entries) {
          final price = entry.value['usd'];
          if (price != null) {
            prices[entry.key] = price.toDouble();
          }
        }
        
        return prices;
      } else {
        throw Exception('Failed to fetch prices: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Network error: $e');
    }
  }
}

// Coin mapping helper
class CoinMapping {
  static const Map<String, String> _coinMap = {
    'BTC': 'bitcoin',
    'ETH': 'ethereum',
    'BNB': 'binancecoin',
    'ADA': 'cardano',
    'SOL': 'solana',
    'XRP': 'ripple',
    'DOT': 'polkadot',
    'DOGE': 'dogecoin',
    'AVAX': 'avalanche-2',
    'SHIB': 'shiba-inu',
    'MATIC': 'matic-network',
    'LTC': 'litecoin',
    'UNI': 'uniswap',
    'LINK': 'chainlink',
    'ATOM': 'cosmos',
  };

  static String getCoinId(String symbol) {
    return _coinMap[symbol.toUpperCase()] ?? symbol.toLowerCase();
  }

  static List<String> getSupportedCoins() {
    return _coinMap.keys.toList();
  }
}
