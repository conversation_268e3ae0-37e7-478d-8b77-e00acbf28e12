import 'package:hive/hive.dart';

part 'trade.g.dart';

@HiveType(typeId: 0)
class Trade extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  DateTime date;

  @HiveField(2)
  String coinName;

  @HiveField(3)
  double buyPrice;

  @HiveField(4)
  double sellPrice;

  @HiveField(5)
  double quantity;

  Trade({
    required this.id,
    required this.date,
    required this.coinName,
    required this.buyPrice,
    required this.sellPrice,
    required this.quantity,
  });

  // Calculated properties
  double get totalBuyValue => buyPrice * quantity;
  double get totalSellValue => sellPrice * quantity;
  double get profitLoss => totalSellValue - totalBuyValue;
  bool get isProfit => profitLoss > 0;
  double get profitPercentage => ((sellPrice - buyPrice) / buyPrice) * 100;

  // Copy with method
  Trade copyWith({
    String? id,
    DateTime? date,
    String? coinName,
    double? buyPrice,
    double? sellPrice,
    double? quantity,
  }) {
    return Trade(
      id: id ?? this.id,
      date: date ?? this.date,
      coinName: coinName ?? this.coinName,
      buyPrice: buyPrice ?? this.buyPrice,
      sellPrice: sellPrice ?? this.sellPrice,
      quantity: quantity ?? this.quantity,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'coinName': coinName,
      'buyPrice': buyPrice,
      'sellPrice': sellPrice,
      'quantity': quantity,
    };
  }

  // From JSON
  factory Trade.fromJson(Map<String, dynamic> json) {
    return Trade(
      id: json['id'],
      date: DateTime.parse(json['date']),
      coinName: json['coinName'],
      buyPrice: json['buyPrice'].toDouble(),
      sellPrice: json['sellPrice'].toDouble(),
      quantity: json['quantity'].toDouble(),
    );
  }

  @override
  String toString() {
    return 'Trade(id: $id, date: $date, coinName: $coinName, buyPrice: $buyPrice, sellPrice: $sellPrice, quantity: $quantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Trade && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
