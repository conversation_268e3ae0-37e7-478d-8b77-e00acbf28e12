import '../datasources/local_data_source.dart';
import '../datasources/remote_data_source.dart';
import '../models/trade.dart';

abstract class TradeRepository {
  Future<List<Trade>> getAllTrades();
  Future<void> addTrade(Trade trade);
  Future<void> updateTrade(Trade trade);
  Future<void> deleteTrade(String id);
  Future<Trade?> getTradeById(String id);
  Future<void> deleteAllTrades();
  Future<double> getCurrentPrice(String coinSymbol);
  Future<Map<String, double>> getMultiplePrices(List<String> coinSymbols);
}

class TradeRepositoryImpl implements TradeRepository {
  final LocalDataSource localDataSource;
  final RemoteDataSource remoteDataSource;

  TradeRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
  });

  @override
  Future<List<Trade>> getAllTrades() async {
    return await localDataSource.getAllTrades();
  }

  @override
  Future<void> addTrade(Trade trade) async {
    await localDataSource.addTrade(trade);
  }

  @override
  Future<void> updateTrade(Trade trade) async {
    await localDataSource.updateTrade(trade);
  }

  @override
  Future<void> deleteTrade(String id) async {
    await localDataSource.deleteTrade(id);
  }

  @override
  Future<Trade?> getTradeById(String id) async {
    return await localDataSource.getTradeById(id);
  }

  @override
  Future<void> deleteAllTrades() async {
    await localDataSource.deleteAllTrades();
  }

  @override
  Future<double> getCurrentPrice(String coinSymbol) async {
    final coinId = CoinMapping.getCoinId(coinSymbol);
    return await remoteDataSource.getCurrentPrice(coinId);
  }

  @override
  Future<Map<String, double>> getMultiplePrices(List<String> coinSymbols) async {
    final coinIds = coinSymbols.map((symbol) => CoinMapping.getCoinId(symbol)).toList();
    final prices = await remoteDataSource.getMultiplePrices(coinIds);
    
    // Convert back to symbol-based map
    final symbolPrices = <String, double>{};
    for (int i = 0; i < coinSymbols.length; i++) {
      final symbol = coinSymbols[i];
      final coinId = coinIds[i];
      if (prices.containsKey(coinId)) {
        symbolPrices[symbol] = prices[coinId]!;
      }
    }
    
    return symbolPrices;
  }
}
