import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('ar')];

  /// No description provided for @appTitle.
  ///
  /// In ar, this message translates to:
  /// **'حاسبة أرباح العملات الرقمية'**
  String get appTitle;

  /// No description provided for @home.
  ///
  /// In ar, this message translates to:
  /// **'الرئيسية'**
  String get home;

  /// No description provided for @trades.
  ///
  /// In ar, this message translates to:
  /// **'الصفقات'**
  String get trades;

  /// No description provided for @addTrade.
  ///
  /// In ar, this message translates to:
  /// **'إضافة صفقة'**
  String get addTrade;

  /// No description provided for @editTrade.
  ///
  /// In ar, this message translates to:
  /// **'تعديل الصفقة'**
  String get editTrade;

  /// No description provided for @deleteTrade.
  ///
  /// In ar, this message translates to:
  /// **'حذف الصفقة'**
  String get deleteTrade;

  /// No description provided for @date.
  ///
  /// In ar, this message translates to:
  /// **'التاريخ'**
  String get date;

  /// No description provided for @coinName.
  ///
  /// In ar, this message translates to:
  /// **'اسم العملة'**
  String get coinName;

  /// No description provided for @buyPrice.
  ///
  /// In ar, this message translates to:
  /// **'سعر الشراء'**
  String get buyPrice;

  /// No description provided for @sellPrice.
  ///
  /// In ar, this message translates to:
  /// **'سعر البيع'**
  String get sellPrice;

  /// No description provided for @quantity.
  ///
  /// In ar, this message translates to:
  /// **'الكمية'**
  String get quantity;

  /// No description provided for @totalBuyValue.
  ///
  /// In ar, this message translates to:
  /// **'إجمالي قيمة الشراء'**
  String get totalBuyValue;

  /// No description provided for @totalSellValue.
  ///
  /// In ar, this message translates to:
  /// **'إجمالي قيمة البيع'**
  String get totalSellValue;

  /// No description provided for @profitLoss.
  ///
  /// In ar, this message translates to:
  /// **'الربح/الخسارة'**
  String get profitLoss;

  /// No description provided for @profit.
  ///
  /// In ar, this message translates to:
  /// **'ربح'**
  String get profit;

  /// No description provided for @loss.
  ///
  /// In ar, this message translates to:
  /// **'خسارة'**
  String get loss;

  /// No description provided for @totalProfit.
  ///
  /// In ar, this message translates to:
  /// **'إجمالي الأرباح'**
  String get totalProfit;

  /// No description provided for @totalLoss.
  ///
  /// In ar, this message translates to:
  /// **'إجمالي الخسائر'**
  String get totalLoss;

  /// No description provided for @netProfitLoss.
  ///
  /// In ar, this message translates to:
  /// **'صافي الربح/الخسارة'**
  String get netProfitLoss;

  /// No description provided for @save.
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In ar, this message translates to:
  /// **'حذف'**
  String get delete;

  /// No description provided for @edit.
  ///
  /// In ar, this message translates to:
  /// **'تعديل'**
  String get edit;

  /// No description provided for @confirm.
  ///
  /// In ar, this message translates to:
  /// **'تأكيد'**
  String get confirm;

  /// No description provided for @confirmDelete.
  ///
  /// In ar, this message translates to:
  /// **'هل أنت متأكد من حذف هذه الصفقة؟'**
  String get confirmDelete;

  /// No description provided for @tradeAdded.
  ///
  /// In ar, this message translates to:
  /// **'تم إضافة الصفقة بنجاح'**
  String get tradeAdded;

  /// No description provided for @tradeUpdated.
  ///
  /// In ar, this message translates to:
  /// **'تم تحديث الصفقة بنجاح'**
  String get tradeUpdated;

  /// No description provided for @tradeDeleted.
  ///
  /// In ar, this message translates to:
  /// **'تم حذف الصفقة بنجاح'**
  String get tradeDeleted;

  /// No description provided for @error.
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// No description provided for @success.
  ///
  /// In ar, this message translates to:
  /// **'نجح'**
  String get success;

  /// No description provided for @pleaseEnterValidData.
  ///
  /// In ar, this message translates to:
  /// **'يرجى إدخال بيانات صحيحة'**
  String get pleaseEnterValidData;

  /// No description provided for @noTradesYet.
  ///
  /// In ar, this message translates to:
  /// **'لا توجد صفقات بعد'**
  String get noTradesYet;

  /// No description provided for @addFirstTrade.
  ///
  /// In ar, this message translates to:
  /// **'أضف أول صفقة لك'**
  String get addFirstTrade;

  /// No description provided for @statistics.
  ///
  /// In ar, this message translates to:
  /// **'الإحصائيات'**
  String get statistics;

  /// No description provided for @chart.
  ///
  /// In ar, this message translates to:
  /// **'الرسم البياني'**
  String get chart;

  /// No description provided for @darkMode.
  ///
  /// In ar, this message translates to:
  /// **'الوضع الليلي'**
  String get darkMode;

  /// No description provided for @lightMode.
  ///
  /// In ar, this message translates to:
  /// **'الوضع النهاري'**
  String get lightMode;

  /// No description provided for @settings.
  ///
  /// In ar, this message translates to:
  /// **'الإعدادات'**
  String get settings;

  /// No description provided for @currentPrice.
  ///
  /// In ar, this message translates to:
  /// **'السعر الحالي'**
  String get currentPrice;

  /// No description provided for @loading.
  ///
  /// In ar, this message translates to:
  /// **'جاري التحميل...'**
  String get loading;

  /// No description provided for @refresh.
  ///
  /// In ar, this message translates to:
  /// **'تحديث'**
  String get refresh;

  /// No description provided for @usd.
  ///
  /// In ar, this message translates to:
  /// **'دولار أمريكي'**
  String get usd;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
