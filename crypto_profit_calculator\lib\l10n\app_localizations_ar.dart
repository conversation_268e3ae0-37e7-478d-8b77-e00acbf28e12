// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'حاسبة أرباح العملات الرقمية';

  @override
  String get home => 'الرئيسية';

  @override
  String get trades => 'الصفقات';

  @override
  String get addTrade => 'إضافة صفقة';

  @override
  String get editTrade => 'تعديل الصفقة';

  @override
  String get deleteTrade => 'حذف الصفقة';

  @override
  String get date => 'التاريخ';

  @override
  String get coinName => 'اسم العملة';

  @override
  String get buyPrice => 'سعر الشراء';

  @override
  String get sellPrice => 'سعر البيع';

  @override
  String get quantity => 'الكمية';

  @override
  String get totalBuyValue => 'إجمالي قيمة الشراء';

  @override
  String get totalSellValue => 'إجمالي قيمة البيع';

  @override
  String get profitLoss => 'الربح/الخسارة';

  @override
  String get profit => 'ربح';

  @override
  String get loss => 'خسارة';

  @override
  String get totalProfit => 'إجمالي الأرباح';

  @override
  String get totalLoss => 'إجمالي الخسائر';

  @override
  String get netProfitLoss => 'صافي الربح/الخسارة';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get confirm => 'تأكيد';

  @override
  String get confirmDelete => 'هل أنت متأكد من حذف هذه الصفقة؟';

  @override
  String get tradeAdded => 'تم إضافة الصفقة بنجاح';

  @override
  String get tradeUpdated => 'تم تحديث الصفقة بنجاح';

  @override
  String get tradeDeleted => 'تم حذف الصفقة بنجاح';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get pleaseEnterValidData => 'يرجى إدخال بيانات صحيحة';

  @override
  String get noTradesYet => 'لا توجد صفقات بعد';

  @override
  String get addFirstTrade => 'أضف أول صفقة لك';

  @override
  String get statistics => 'الإحصائيات';

  @override
  String get chart => 'الرسم البياني';

  @override
  String get darkMode => 'الوضع الليلي';

  @override
  String get lightMode => 'الوضع النهاري';

  @override
  String get settings => 'الإعدادات';

  @override
  String get currentPrice => 'السعر الحالي';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get refresh => 'تحديث';

  @override
  String get usd => 'دولار أمريكي';
}
