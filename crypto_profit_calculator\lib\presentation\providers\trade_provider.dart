import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../data/datasources/local_data_source.dart';
import '../../data/datasources/remote_data_source.dart';
import '../../data/repositories/trade_repository.dart';
import '../../data/models/trade.dart';

class TradeProvider extends ChangeNotifier {
  late final TradeRepository _repository;
  
  List<Trade> _trades = [];
  bool _isLoading = false;
  String? _error;
  Map<String, double> _currentPrices = {};
  bool _isPriceLoading = false;

  TradeProvider() {
    _repository = TradeRepositoryImpl(
      localDataSource: LocalDataSourceImpl(),
      remoteDataSource: RemoteDataSourceImpl(client: http.Client()),
    );
    loadTrades();
  }

  // Getters
  List<Trade> get trades => _trades;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, double> get currentPrices => _currentPrices;
  bool get isPriceLoading => _isPriceLoading;

  // Statistics
  double get totalProfit {
    return _trades.where((trade) => trade.isProfit).fold(0.0, (sum, trade) => sum + trade.profitLoss);
  }

  double get totalLoss {
    return _trades.where((trade) => !trade.isProfit).fold(0.0, (sum, trade) => sum + trade.profitLoss.abs());
  }

  double get netProfitLoss {
    return _trades.fold(0.0, (sum, trade) => sum + trade.profitLoss);
  }

  int get totalTrades => _trades.length;
  int get profitableTrades => _trades.where((trade) => trade.isProfit).length;
  int get losingTrades => _trades.where((trade) => !trade.isProfit).length;

  double get winRate {
    if (totalTrades == 0) return 0.0;
    return (profitableTrades / totalTrades) * 100;
  }

  // Load all trades
  Future<void> loadTrades() async {
    _setLoading(true);
    _clearError();
    
    try {
      _trades = await _repository.getAllTrades();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الصفقات: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Add new trade
  Future<bool> addTrade(Trade trade) async {
    _clearError();
    
    try {
      await _repository.addTrade(trade);
      _trades.insert(0, trade); // Add to beginning for newest first
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في إضافة الصفقة: $e');
      return false;
    }
  }

  // Update existing trade
  Future<bool> updateTrade(Trade trade) async {
    _clearError();
    
    try {
      await _repository.updateTrade(trade);
      final index = _trades.indexWhere((t) => t.id == trade.id);
      if (index != -1) {
        _trades[index] = trade;
        notifyListeners();
      }
      return true;
    } catch (e) {
      _setError('فشل في تحديث الصفقة: $e');
      return false;
    }
  }

  // Delete trade
  Future<bool> deleteTrade(String id) async {
    _clearError();
    
    try {
      await _repository.deleteTrade(id);
      _trades.removeWhere((trade) => trade.id == id);
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في حذف الصفقة: $e');
      return false;
    }
  }

  // Get trade by ID
  Trade? getTradeById(String id) {
    try {
      return _trades.firstWhere((trade) => trade.id == id);
    } catch (e) {
      return null;
    }
  }

  // Delete all trades
  Future<bool> deleteAllTrades() async {
    _clearError();
    
    try {
      await _repository.deleteAllTrades();
      _trades.clear();
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في حذف جميع الصفقات: $e');
      return false;
    }
  }

  // Get current price for a coin
  Future<double?> getCurrentPrice(String coinSymbol) async {
    try {
      final price = await _repository.getCurrentPrice(coinSymbol);
      _currentPrices[coinSymbol] = price;
      notifyListeners();
      return price;
    } catch (e) {
      _setError('فشل في جلب السعر الحالي: $e');
      return null;
    }
  }

  // Load current prices for all unique coins in trades
  Future<void> loadCurrentPrices() async {
    if (_trades.isEmpty) return;
    
    _setPriceLoading(true);
    _clearError();
    
    try {
      final uniqueCoins = _trades.map((trade) => trade.coinName).toSet().toList();
      final prices = await _repository.getMultiplePrices(uniqueCoins);
      _currentPrices = prices;
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل الأسعار الحالية: $e');
    } finally {
      _setPriceLoading(false);
    }
  }

  // Get trades grouped by coin
  Map<String, List<Trade>> getTradesGroupedByCoin() {
    final grouped = <String, List<Trade>>{};
    for (final trade in _trades) {
      if (!grouped.containsKey(trade.coinName)) {
        grouped[trade.coinName] = [];
      }
      grouped[trade.coinName]!.add(trade);
    }
    return grouped;
  }

  // Get profit/loss data for charts
  List<Map<String, dynamic>> getProfitLossChartData() {
    final data = <Map<String, dynamic>>[];
    double cumulativeProfit = 0.0;
    
    // Sort trades by date
    final sortedTrades = List<Trade>.from(_trades)
      ..sort((a, b) => a.date.compareTo(b.date));
    
    for (final trade in sortedTrades) {
      cumulativeProfit += trade.profitLoss;
      data.add({
        'date': trade.date,
        'profit': trade.profitLoss,
        'cumulative': cumulativeProfit,
        'coinName': trade.coinName,
      });
    }
    
    return data;
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setPriceLoading(bool loading) {
    _isPriceLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
