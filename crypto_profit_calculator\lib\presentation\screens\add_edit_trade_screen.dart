import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../core/localization/app_localizations.dart';
import '../../core/theme/app_colors.dart';
import '../../data/models/trade.dart';
import '../../data/datasources/remote_data_source.dart';
import '../providers/trade_provider.dart';

class AddEditTradeScreen extends StatefulWidget {
  final String? tradeId;

  const AddEditTradeScreen({super.key, this.tradeId});

  @override
  State<AddEditTradeScreen> createState() => _AddEditTradeScreenState();
}

class _AddEditTradeScreenState extends State<AddEditTradeScreen> {
  final _formKey = GlobalKey<FormState>();
  final _coinNameController = TextEditingController();
  final _buyPriceController = TextEditingController();
  final _sellPriceController = TextEditingController();
  final _quantityController = TextEditingController();
  
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;
  bool _isPriceLoading = false;
  Trade? _existingTrade;

  @override
  void initState() {
    super.initState();
    if (widget.tradeId != null) {
      _loadExistingTrade();
    }
  }

  void _loadExistingTrade() {
    final tradeProvider = context.read<TradeProvider>();
    _existingTrade = tradeProvider.getTradeById(widget.tradeId!);
    
    if (_existingTrade != null) {
      _coinNameController.text = _existingTrade!.coinName;
      _buyPriceController.text = _existingTrade!.buyPrice.toString();
      _sellPriceController.text = _existingTrade!.sellPrice.toString();
      _quantityController.text = _existingTrade!.quantity.toString();
      _selectedDate = _existingTrade!.date;
    }
  }

  @override
  void dispose() {
    _coinNameController.dispose();
    _buyPriceController.dispose();
    _sellPriceController.dispose();
    _quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isEditing = widget.tradeId != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(isEditing ? localizations.editTrade : localizations.addTrade),
        actions: [
          if (!isEditing)
            IconButton(
              onPressed: _isPriceLoading ? null : _fetchCurrentPrice,
              icon: _isPriceLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : const Icon(Icons.price_change),
              tooltip: localizations.currentPrice,
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Date Selection
              Card(
                child: ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: Text(localizations.date),
                  subtitle: Text(
                    DateFormat('dd/MM/yyyy', 'ar_SA').format(_selectedDate),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectDate,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Coin Name
              _buildCoinNameField(localizations),
              
              const SizedBox(height: 16),
              
              // Buy Price
              _buildPriceField(
                controller: _buyPriceController,
                label: localizations.buyPrice,
                icon: Icons.shopping_cart,
              ),
              
              const SizedBox(height: 16),
              
              // Sell Price
              _buildPriceField(
                controller: _sellPriceController,
                label: localizations.sellPrice,
                icon: Icons.sell,
              ),
              
              const SizedBox(height: 16),
              
              // Quantity
              _buildQuantityField(localizations),
              
              const SizedBox(height: 24),
              
              // Calculation Preview
              _buildCalculationPreview(localizations),
              
              const SizedBox(height: 32),
              
              // Save Button
              ElevatedButton(
                onPressed: _isLoading ? null : _saveTrade,
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: Colors.white,
                        ),
                      )
                    : Text(localizations.save),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoinNameField(AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _coinNameController,
          decoration: InputDecoration(
            labelText: localizations.coinName,
            hintText: 'BTC, ETH, BNB...',
            prefixIcon: const Icon(Icons.currency_bitcoin),
            suffixIcon: PopupMenuButton<String>(
              onSelected: (value) {
                _coinNameController.text = value;
                _fetchCurrentPrice();
              },
              itemBuilder: (context) => CoinMapping.getSupportedCoins()
                  .map((coin) => PopupMenuItem(
                        value: coin,
                        child: Text(coin),
                      ))
                  .toList(),
              child: const Icon(Icons.arrow_drop_down),
            ),
          ),
          textCapitalization: TextCapitalization.characters,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return localizations.pleaseEnterValidData;
            }
            return null;
          },
          onChanged: (value) {
            if (value.length >= 2) {
              _fetchCurrentPrice();
            }
          },
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: CoinMapping.getSupportedCoins().take(6).map((coin) {
            return ActionChip(
              label: Text(coin),
              onPressed: () {
                _coinNameController.text = coin;
                _fetchCurrentPrice();
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriceField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: '0.00',
        prefixIcon: Icon(icon),
        suffixText: 'USD',
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return AppLocalizations.of(context)!.pleaseEnterValidData;
        }
        final price = double.tryParse(value);
        if (price == null || price <= 0) {
          return AppLocalizations.of(context)!.pleaseEnterValidData;
        }
        return null;
      },
      onChanged: (value) => setState(() {}), // Trigger calculation update
    );
  }

  Widget _buildQuantityField(AppLocalizations localizations) {
    return TextFormField(
      controller: _quantityController,
      decoration: InputDecoration(
        labelText: localizations.quantity,
        hintText: '0.00',
        prefixIcon: const Icon(Icons.numbers),
      ),
      keyboardType: const TextInputType.numberWithOptions(decimal: true),
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return localizations.pleaseEnterValidData;
        }
        final quantity = double.tryParse(value);
        if (quantity == null || quantity <= 0) {
          return localizations.pleaseEnterValidData;
        }
        return null;
      },
      onChanged: (value) => setState(() {}), // Trigger calculation update
    );
  }

  Widget _buildCalculationPreview(AppLocalizations localizations) {
    final buyPrice = double.tryParse(_buyPriceController.text) ?? 0;
    final sellPrice = double.tryParse(_sellPriceController.text) ?? 0;
    final quantity = double.tryParse(_quantityController.text) ?? 0;

    final totalBuyValue = buyPrice * quantity;
    final totalSellValue = sellPrice * quantity;
    final profitLoss = totalSellValue - totalBuyValue;
    final profitLossPercentage = buyPrice > 0 ? ((sellPrice - buyPrice) / buyPrice) * 100 : 0;

    final currencyFormatter = NumberFormat.currency(
      locale: 'ar_SA',
      symbol: '\$',
      decimalDigits: 2,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معاينة الحساب',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            _buildCalculationRow(
              localizations.totalBuyValue,
              currencyFormatter.format(totalBuyValue),
              Icons.shopping_cart,
            ),

            _buildCalculationRow(
              localizations.totalSellValue,
              currencyFormatter.format(totalSellValue),
              Icons.sell,
            ),

            const Divider(),

            _buildCalculationRow(
              localizations.profitLoss,
              currencyFormatter.format(profitLoss.abs()),
              profitLoss >= 0 ? Icons.trending_up : Icons.trending_down,
              color: AppColors.getProfitLossColor(profitLoss),
              isProfit: profitLoss >= 0,
            ),

            _buildCalculationRow(
              '${localizations.profitLoss} %',
              '${profitLossPercentage.toStringAsFixed(2)}%',
              profitLoss >= 0 ? Icons.trending_up : Icons.trending_down,
              color: AppColors.getProfitLossColor(profitLoss),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalculationRow(
    String label,
    String value,
    IconData icon, {
    Color? color,
    bool? isProfit,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: color ?? AppColors.textSecondary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isProfit != null) ...[
                Text(
                  isProfit ? 'ربح' : 'خسارة',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      locale: const Locale('ar', 'SA'),
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _fetchCurrentPrice() async {
    final coinName = _coinNameController.text.trim().toUpperCase();
    if (coinName.isEmpty) return;

    setState(() {
      _isPriceLoading = true;
    });

    try {
      final tradeProvider = context.read<TradeProvider>();
      final price = await tradeProvider.getCurrentPrice(coinName);

      if (price != null && mounted) {
        // Auto-fill sell price with current price if it's empty
        if (_sellPriceController.text.isEmpty) {
          _sellPriceController.text = price.toStringAsFixed(2);
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${AppLocalizations.of(context)!.currentPrice}: \$${price.toStringAsFixed(2)}',
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في جلب السعر الحالي'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isPriceLoading = false;
        });
      }
    }
  }

  Future<void> _saveTrade() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final trade = Trade(
        id: _existingTrade?.id ?? DateTime.now().millisecondsSinceEpoch.toString(),
        date: _selectedDate,
        coinName: _coinNameController.text.trim().toUpperCase(),
        buyPrice: double.parse(_buyPriceController.text),
        sellPrice: double.parse(_sellPriceController.text),
        quantity: double.parse(_quantityController.text),
      );

      final tradeProvider = context.read<TradeProvider>();
      final success = _existingTrade != null
          ? await tradeProvider.updateTrade(trade)
          : await tradeProvider.addTrade(trade);

      if (success && mounted) {
        final localizations = AppLocalizations.of(context)!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _existingTrade != null
                  ? localizations.tradeUpdated
                  : localizations.tradeAdded,
            ),
            backgroundColor: AppColors.success,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
