name: crypto_profit_calculator
description: تطبيق حساب الربح والخسارة في تداول العملات الرقمية
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # Local Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Charts
  fl_chart: ^0.65.0
  
  # Internationalization
  intl: ^0.20.2
  
  # HTTP requests for API
  http: ^1.1.0
  
  # UI Components
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  
  # Code generation for Hive
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  generate: true

  # Assets
  # assets:
  #   - assets/images/
  #   - assets/icons/

  # Fonts (commented out until font files are added)
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
