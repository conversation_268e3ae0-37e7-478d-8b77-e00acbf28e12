@echo off
echo ========================================
echo   تطبيق حاسبة أرباح العملات الرقمية
echo ========================================
echo.

echo التحقق من تثبيت Flutter...
flutter --version
if %errorlevel% neq 0 (
    echo خطأ: Flutter غير مثبت على النظام
    echo يرجى تثبيت Flutter من: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo.
echo تثبيت التبعيات...
flutter pub get
if %errorlevel% neq 0 (
    echo خطأ في تثبيت التبعيات
    pause
    exit /b 1
)

echo.
echo توليد الملفات المطلوبة...
flutter packages pub run build_runner build --delete-conflicting-outputs
if %errorlevel% neq 0 (
    echo تحذير: فشل في توليد بعض الملفات، لكن يمكن المتابعة
)

echo.
echo تشغيل التطبيق...
flutter run

pause
