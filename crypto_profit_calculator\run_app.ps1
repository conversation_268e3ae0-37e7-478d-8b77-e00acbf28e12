
# تطبيق حاسبة أرباح العملات الرقمية
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   تطبيق حاسبة أرباح العملات الرقمية" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔍 تشخيص النظام..." -ForegroundColor Yellow

# التحقق من وجود Flutter في PATH
$flutterPath = Get-Command flutter -ErrorAction SilentlyContinue
if (-not $flutterPath) {
    Write-Host "❌ خطأ: Flutter غير مثبت أو غير موجود في PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 خطوات الحل:" -ForegroundColor Yellow
    Write-Host "1. حمل Flutter من: https://flutter.dev/docs/get-started/install/windows" -ForegroundColor White
    Write-Host "2. استخرج الملفات إلى C:\flutter" -ForegroundColor White
    Write-Host "3. أضف C:\flutter\bin إلى متغير PATH في إعدادات النظام" -ForegroundColor White
    Write-Host "4. أعد تشغيل PowerShell أو Command Prompt" -ForegroundColor White
    Write-Host ""
    Write-Host "📋 للتحقق من PATH الحالي، شغل هذا الأمر:" -ForegroundColor Cyan
    Write-Host '$env:PATH -split ";" | Select-String flutter' -ForegroundColor Gray
    Write-Host ""
    Write-Host "🎯 أو استخدم هذا الأمر لإضافة Flutter مؤقتاً:" -ForegroundColor Cyan
    Write-Host '$env:PATH += ";C:\flutter\bin"' -ForegroundColor Gray
    Write-Host ""
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ Flutter موجود في النظام" -ForegroundColor Green

# التحقق من إصدار Flutter
try {
    Write-Host "📋 معلومات Flutter:" -ForegroundColor Cyan
    flutter --version
    Write-Host ""
} catch {
    Write-Host "❌ خطأ في تشغيل Flutter" -ForegroundColor Red
    Write-Host "الخطأ: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# التحقق من الأجهزة المتاحة
Write-Host "📱 التحقق من الأجهزة المتاحة..." -ForegroundColor Yellow
try {
    $devices = flutter devices 2>&1
    if ($devices -match "No devices detected") {
        Write-Host "⚠️  تحذير: لا توجد أجهزة متصلة" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "🔧 الحلول:" -ForegroundColor Cyan
        Write-Host "• وصل جهاز Android مع تفعيل USB Debugging" -ForegroundColor White
        Write-Host "• أو شغل Android Emulator من Android Studio" -ForegroundColor White
        Write-Host "• أو استخدم Chrome للـ Web: flutter run -d chrome" -ForegroundColor White
        Write-Host ""
    } else {
        Write-Host "✅ تم العثور على أجهزة:" -ForegroundColor Green
        Write-Host $devices
    }
} catch {
    Write-Host "⚠️  تحذير: لا يمكن التحقق من الأجهزة" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 بدء تثبيت التبعيات..." -ForegroundColor Yellow

# تثبيت التبعيات
try {
    flutter pub get
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم تثبيت التبعيات بنجاح" -ForegroundColor Green
    } else {
        Write-Host "❌ خطأ في تثبيت التبعيات" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "❌ خطأ في تثبيت التبعيات: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "🔨 توليد الملفات المطلوبة..." -ForegroundColor Yellow

# توليد ملفات Hive
try {
    flutter packages pub run build_runner build --delete-conflicting-outputs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ تم توليد الملفات بنجاح" -ForegroundColor Green
    } else {
        Write-Host "⚠️  تحذير: فشل في توليد بعض الملفات، لكن يمكن المتابعة" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  تحذير: فشل في توليد الملفات، لكن يمكن المتابعة" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "💡 ملاحظة: تأكد من توصيل جهاز Android أو تشغيل محاكي" -ForegroundColor Cyan
Write-Host ""

# تشغيل التطبيق
try {
    flutter run
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 حلول مقترحة:" -ForegroundColor Yellow
    Write-Host "• تأكد من وجود جهاز متصل: flutter devices" -ForegroundColor White
    Write-Host "• جرب تشغيل على Chrome: flutter run -d chrome" -ForegroundColor White
    Write-Host "• نظف المشروع: flutter clean; flutter pub get" -ForegroundColor White
}

Write-Host ""
Write-Host "🏁 انتهى التشغيل" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
