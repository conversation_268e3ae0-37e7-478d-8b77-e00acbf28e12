# Simple Flutter App Runner
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   Crypto Profit Calculator" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check Flutter
Write-Host "Checking Flutter..." -ForegroundColor Yellow
$flutterPath = Get-Command flutter -ErrorAction SilentlyContinue
if (-not $flutterPath) {
    Write-Host "ERROR: Flutter not found in PATH" -ForegroundColor Red
    Write-Host ""
    Write-Host "Solutions:" -ForegroundColor Yellow
    Write-Host "1. Download Flutter from: https://flutter.dev/docs/get-started/install/windows" -ForegroundColor White
    Write-Host "2. Extract to C:\flutter" -ForegroundColor White
    Write-Host "3. Add C:\flutter\bin to PATH" -ForegroundColor White
    Write-Host "4. Restart PowerShell" -ForegroundColor White
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Flutter found!" -ForegroundColor Green

# Check devices
Write-Host ""
Write-Host "Checking devices..." -ForegroundColor Yellow
try {
    $devices = flutter devices 2>&1
    if ($devices -match "No devices detected") {
        Write-Host "WARNING: No devices connected" -ForegroundColor Yellow
        Write-Host "Solutions:" -ForegroundColor Cyan
        Write-Host "- Connect Android device with USB Debugging" -ForegroundColor White
        Write-Host "- Start Android Emulator" -ForegroundColor White
        Write-Host "- Use Chrome: flutter run -d chrome" -ForegroundColor White
        Write-Host ""
    } else {
        Write-Host "Devices found!" -ForegroundColor Green
    }
} catch {
    Write-Host "Could not check devices" -ForegroundColor Yellow
}

# Install dependencies
Write-Host ""
Write-Host "Installing dependencies..." -ForegroundColor Yellow
try {
    flutter pub get
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Dependencies installed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Error installing dependencies" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Generate files
Write-Host ""
Write-Host "Generating required files..." -ForegroundColor Yellow
try {
    flutter packages pub run build_runner build --delete-conflicting-outputs
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Files generated successfully!" -ForegroundColor Green
    } else {
        Write-Host "Warning: Some files failed to generate, but can continue" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Warning: File generation failed, but can continue" -ForegroundColor Yellow
}

# Run app
Write-Host ""
Write-Host "Starting the app..." -ForegroundColor Yellow
Write-Host "Note: Make sure you have a device connected or emulator running" -ForegroundColor Cyan
Write-Host ""

try {
    flutter run
} catch {
    Write-Host "Error running app: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Suggested solutions:" -ForegroundColor Yellow
    Write-Host "- Check devices: flutter devices" -ForegroundColor White
    Write-Host "- Try Chrome: flutter run -d chrome" -ForegroundColor White
    Write-Host "- Clean project: flutter clean; flutter pub get" -ForegroundColor White
}

Write-Host ""
Write-Host "Done!" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
